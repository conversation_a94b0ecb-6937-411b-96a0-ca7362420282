import React, { useEffect, useState, useRef, useCallback } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, ScrollView, Modal, TextInput, Alert } from 'react-native';
import { getTransactions, deleteTransaction, updateTransaction, getMembers, getTags, getCategories, getTotalBudget, getMonthlyTotals, getSetting } from '../constants/Storage';
import { router, useFocusEffect } from 'expo-router';
import { Swipeable } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import { useTransactionContext } from '../context/TransactionContext';
import i18n from '../i18n';
import EmptyState from './EmptyState';
import { useSettings } from '../context/SettingsContext';
import { useTheme } from '../context/ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface Transaction {
  id: number;
  type: 'income' | 'expense';
  amount: number;
  category: string;
  categoryIcon: string;
  note: string;
  date: string;
  member_id: number;
  refunded: boolean;
  refund_amount?: number;
  reimbursement_status?: 'none' | 'pending' | 'completed';
  tags?: number[];
  exclude_from_budget: boolean;
  shopping_platform: string;
  image_path?: string;
}

interface GroupedTransactions {
  [date: string]: Transaction[];
}

interface DailyTotal {
  income: number;
  expense: number;
}

interface MonthlyTotal {
  income: number;
  expense: number;
}

interface Member {
  id: number;
  name: string;
  budget: number | null;
}

interface Tag {
  id: number;
  name: string;
  color: string;
}

interface Category {
  id: number;
  name: string;
  icon: string;
  type: 'income' | 'expense';
}

const HomeList = () => {
  const [transactions, setTransactions] = useState<GroupedTransactions>({});
  const { refreshTrigger, triggerRefresh } = useTransactionContext();
  const swipeableRefs = useRef<{ [key: number]: Swipeable | null }>({});
  const [activeFilter, setActiveFilter] = useState<'all' | 'income' | 'expense'>('all');
  const [monthlyTotal, setMonthlyTotal] = useState<MonthlyTotal>({ income: 0, expense: 0 });
  const [selectedMembers, setSelectedMembers] = useState<number[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [showMemberSelector, setShowMemberSelector] = useState(false);
  const [tags, setTags] = useState<Tag[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const PAGE_SIZE = 10;
  const { currency, hideBudgetModule, hideMemberSection, hideShoppingPlatformSection, minimalistDisplay } = useSettings();
  const { theme } = useTheme();
  const [incomeCategories, setIncomeCategories] = useState<Category[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<Category[]>([]);
  const [totalBudget, setTotalBudget] = useState<number | null>(null);
  const [actualMonthlyTotals, setActualMonthlyTotals] = useState({ income: 0, expense: 0, budgetExpense: 0 });

  // 退款相关状态
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [showRefundTypeModal, setShowRefundTypeModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [refundAmount, setRefundAmount] = useState('');

  // 个人留言状态
  const [personalNote, setPersonalNote] = useState('');
  const [personalNoteVisible, setPersonalNoteVisible] = useState(false);

  // 保存选中的成员状态到本地存储
  const saveSelectedMembers = async (memberIds: number[]) => {
    try {
      await AsyncStorage.setItem('selectedMembers', JSON.stringify(memberIds));
    } catch (error) {
      console.error('Failed to save selected members:', error);
    }
  };

  // 从本地存储加载选中的成员状态
  const loadSelectedMembers = async () => {
    try {
      const saved = await AsyncStorage.getItem('selectedMembers');
      if (saved) {
        const memberIds = JSON.parse(saved);
        setSelectedMembers(memberIds);
        return memberIds;
      }
    } catch (error) {
      console.error('Failed to load selected members:', error);
    }
    return [];
  };

  // 计算每日总计
  const calculateDailyTotal = (transactions: Transaction[]): DailyTotal => {
    return transactions.reduce((total, transaction) => {
      let effectiveAmount = Math.abs(transaction.amount);

      // 处理部分退款：如果有退款金额，从原金额中减去退款金额
      if (transaction.refund_amount && transaction.refund_amount > 0) {
        effectiveAmount = Math.max(0, effectiveAmount - transaction.refund_amount);
      } else if (transaction.refunded) {
        // 完全退款的情况，跳过计算
        return total;
      }

      if (transaction.type === 'income') {
        total.income += effectiveAmount;
      } else {
        total.expense += effectiveAmount;
      }
      return total;
    }, { income: 0, expense: 0 });
  };

  // 加载实际的月度总计（不受搜索和分页影响）
  const loadActualMonthlyTotals = async () => {
    try {
      const totals = await getMonthlyTotals(selectedMembers.length === 0 ? [] : selectedMembers);
      setActualMonthlyTotals(totals);
      // 同时更新旧的monthlyTotal以保持兼容性
      setMonthlyTotal({ income: totals.income, expense: totals.expense });
    } catch (error) {
      console.error('Failed to load monthly totals:', error);
      // 设置默认值以防出错
      setActualMonthlyTotals({ income: 0, expense: 0, budgetExpense: 0 });
    }
  };

  // 计算月度总计（保留用于向后兼容，但现在使用实际数据库查询）
  const calculateMonthlyTotal = (data: Transaction[]) => {
    // 这个函数现在主要用于向后兼容，实际数据由loadActualMonthlyTotals提供
    // 但我们仍然保留它以防某些地方需要基于当前加载数据的计算
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    const total = data.reduce((acc, transaction) => {
      const transactionDate = new Date(transaction.date);
      if (
        transactionDate.getMonth() === currentMonth &&
        transactionDate.getFullYear() === currentYear
      ) {
        let effectiveAmount = Math.abs(transaction.amount);

        // 处理部分退款：如果有退款金额，从原金额中减去退款金额
        if (transaction.refund_amount && transaction.refund_amount > 0) {
          effectiveAmount = Math.max(0, effectiveAmount - transaction.refund_amount);
        } else if (transaction.refunded) {
          // 完全退款的情况，跳过计算
          return acc;
        }

        if (transaction.type === 'income') {
          acc.income += effectiveAmount;
        } else {
          acc.expense += effectiveAmount;
        }
      }
      return acc;
    }, { income: 0, expense: 0 });

    // 不再设置monthlyTotal，因为我们使用actualMonthlyTotals
  };

  // 加载成员数据
  const loadMembers = async () => {
    try {
      const data = await getMembers();
      setMembers(data);
    } catch (error) {
      console.error('Failed to load members:', error);
    }
  };

  // 加载标签数据
  const loadTags = async () => {
    try {
      const data = await getTags();
      setTags(data);
    } catch (error) {
      console.error('Failed to load tags:', error);
    }
  };

  // 加载分类数据
  const loadCategories = async () => {
    try {
      const categories = await getCategories();
      setIncomeCategories(categories.filter(c => c.type === 'income') as Category[]);
      setExpenseCategories(categories.filter(c => c.type === 'expense') as Category[]);
    } catch (error) {
      console.error('Failed to load categories:', error);
    }
  };

  // 添加 useFocusEffect 钩子，在页面获得焦点时刷新数据
  useFocusEffect(
    useCallback(() => {
      // 当页面获得焦点时（例如从预算页面返回时）刷新数据
      loadMembers();
      loadTags();
      loadCategories();
      loadTotalBudget();
      loadSelectedMembers(); // 加载保存的成员选择状态
      loadPersonalNote(); // 重新加载个人留言
      return () => {
        // 可选的清理函数
      };
    }, [])
  );

  // 保留现有的 useEffect 钩子
  useEffect(() => {
    loadMembers();
    loadTags();
    loadCategories();
  }, [refreshTrigger]);

  // 计算所有选中成员的统计数据
  const calculateSelectedMembersStats = () => {
    // 使用实际的月度总计，而不是当前加载的交易数据
    const expenses = actualMonthlyTotals.budgetExpense;

    const totalBudget = selectedMembers.length === 0
      ? members.reduce((sum, m) => sum + (m.budget || 0), 0)
      : members
        .filter(m => selectedMembers.includes(m.id))
        .reduce((sum, m) => sum + (m.budget || 0), 0);

    return {
      expenses: expenses,
      budget: totalBudget,
      remaining: totalBudget - expenses
    };
  };

  const loadTransactions = async (pageNum: number, replace = false) => {
    // 如果正在加载中，直接返回
    if (isLoading) return;

    try {
      setIsLoading(true);

      const { transactions: newTransactions, hasMore: more } = await getTransactions({
        page: pageNum,
        pageSize: PAGE_SIZE,
        filter: activeFilter === 'all' ? undefined : activeFilter,
        memberIds: selectedMembers,
        searchText: searchText
      });

      setHasMore(more);
      const grouped = newTransactions as any

      if (replace) {
        setTransactions(grouped);
        // 加载实际的月度总计
        loadActualMonthlyTotals();
      } else {
        setTransactions(prev => {
          const newState = { ...prev };
          Object.entries(grouped).forEach(([date, items]) => {
            if (newState[date]) {
              // 检查并过滤掉重复的交易记录
              const existingIds = new Set(newState[date].map((t: Transaction) => t.id));
              const uniqueItems = (items as Transaction[]).filter(item => !existingIds.has(item.id));
              newState[date] = [...newState[date], ...uniqueItems];
            } else {
              newState[date] = items as Transaction[];
            }
          });
          return newState;
        });
      }

      setPage(pageNum);
    } catch (error) {
      console.error('Failed to load transactions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化数据加载
  useEffect(() => {
    loadActualMonthlyTotals();
  }, []);

  // 监听筛选条件变化
  useEffect(() => {
    loadTransactions(1, true);
  }, [activeFilter, selectedMembers, refreshTrigger]);

  // 监听选中成员变化，重新加载月度总计
  useEffect(() => {
    loadActualMonthlyTotals();
  }, [selectedMembers, refreshTrigger]);

  // 监听搜索文本变化
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      loadTransactions(1, true);
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchText]);

  // 关闭所有打开的左滑菜单
  const closeAllSwipeables = () => {
    Object.values(swipeableRefs.current).forEach(ref => {
      ref?.close();
    });
  };

  // 监听页面触摸事件来关闭左滑菜单
  useEffect(() => {
    const handleTouchStart = () => {
      closeAllSwipeables();
    };
  }, []);

  const renderRightActions = (transaction: Transaction) => {
    // 根据交易状态决定显示哪些按钮
    // 显示退款按钮的条件：是支出（即使已经全额退款也显示，防止用户误操作）
    const showRefundButton = transaction.type === 'expense';

    // 显示报销按钮的条件：是支出且未标记为报销
    const canReimburse = (transaction.type === 'expense') && (transaction.reimbursement_status !== 'completed');
    const showReimbursementButton = canReimburse;

    let buttonCount = 2; // 编辑和删除
    if (showRefundButton) buttonCount++;
    if (showReimbursementButton) buttonCount++;

    return (
      <View style={[styles.actionButtons, { width: buttonCount * 60 }]}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.editButton,
            !showRefundButton && !showReimbursementButton && { borderTopLeftRadius: 12, borderBottomLeftRadius: 12 }
          ]}
          onPress={() => {
            handleEdit(transaction);
          }}
        >
          <Ionicons name="pencil-outline" size={20} color="white" />
          <Text style={styles.actionButtonText}>{i18n.t('common.edit')}</Text>
        </TouchableOpacity>

        {showRefundButton && (
          <TouchableOpacity
            style={[styles.actionButton, styles.refundButton]}
            onPress={() => {
              setSelectedTransaction(transaction);
              setRefundAmount('');
              setShowRefundTypeModal(true);
            }}
          >
            <Ionicons name="arrow-undo-outline" size={20} color="white" />
            <Text style={styles.actionButtonText}>{i18n.t('common.refund')}</Text>
          </TouchableOpacity>
        )}

        {/* {!!showReimbursementButton && (
          <TouchableOpacity
            style={[styles.actionButton, styles.reimbursementButton]}
            onPress={() => handleToggleReimbursement(transaction)}
          >
            <Ionicons name="receipt-outline" size={20} color="white" />
            <Text style={styles.actionButtonText}>
              {transaction.reimbursement_status === 'pending' ? i18n.t('reimbursement.cancelReimbursement') : i18n.t('reimbursement.reimburse')}
            </Text>
          </TouchableOpacity>
        )} */}

        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.deleteButton,
            { borderTopRightRadius: 12, borderBottomRightRadius: 12 }
          ]}
          onPress={() => handleDeleteTransaction(transaction)}
        >
          <Ionicons name="trash-outline" size={20} color="white" />
          <Text style={styles.actionButtonText}>{i18n.t('common.delete')}</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const formatDate = (dateString: string) => {
    // 确保使用本地时间解析日期，避免时区问题
    const date = new Date(dateString + 'T00:00:00');
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    // 比较日期时使用本地日期字符串，避免时区影响
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    const yesterdayStr = `${yesterday.getFullYear()}-${String(yesterday.getMonth() + 1).padStart(2, '0')}-${String(yesterday.getDate()).padStart(2, '0')}`;

    if (dateStr === todayStr) {
      return i18n.t('common.today');
    } else if (dateStr === yesterdayStr) {
      return i18n.t('common.yesterday');
    } else {
      // 根据当前语言格式化日期
      if (i18n.locale === 'zh') {
        return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
      } else {
        // 英文日期格式
        const options: Intl.DateTimeFormatOptions = {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        };
        return date.toLocaleDateString(i18n.locale, options);
      }
    }
  };

  // 获取交易的标签
  const getTransactionTags = (transaction: Transaction) => {
    if (!transaction.tags) return [];
    return tags.filter(tag => transaction.tags?.includes(tag.id));
  };

  // 渲染标签
  const renderTags = (transaction: Transaction) => {
    if (!transaction.tags || transaction.tags.length === 0) return null;

    const MAX_VISIBLE_TAGS = 2; // 首页最多显示2个标签
    const MAX_TAG_LENGTH = 6; // 标签文本最大长度

    // 截取标签文本
    const truncateText = (text: string, maxLength: number) => {
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    };

    const tagData = transaction.tags.map(tagId => {
      const tag = tags.find(t => t.id === tagId);
      return tag || { id: tagId, name: `Tag ${tagId}`, color: '#ccc' };
    });

    const visibleTags = tagData.slice(0, MAX_VISIBLE_TAGS);
    const remainingCount = tagData.length - MAX_VISIBLE_TAGS;

    return (
      <View style={styles.tagContainer}>
        {visibleTags.map(tag => (
          <View key={tag.id} style={[styles.tag, { borderColor: tag.color }]}>
            <Text style={[styles.tagText, { color: tag.color }]}>
              {truncateText(tag.name, MAX_TAG_LENGTH)}
            </Text>
          </View>
        ))}
        {remainingCount > 0 && (
          <View style={styles.moreTagsContainer}>
            <Text style={styles.moreTagsText}>+{remainingCount}</Text>
          </View>
        )}
      </View>
    );
  };

  const renderTransactionItem = (transaction: Transaction, index: number) => {
    // 获取最新的分类信息
    const categoryInfo = getCategoryInfo(transaction.category, transaction.type);

    // 获取成员名称，如果 member_id 为 0，则不显示成员
    const memberName = transaction.member_id === 0
      ? null
      : members.find(m => m.id === transaction.member_id)?.name;

    // 使用组合键确保唯一性
    const uniqueKey = `${transaction.id}`;

    // 如果是极简模式，渲染极简版本
    if (minimalistDisplay) {
      return (
        <Swipeable
          key={uniqueKey}
          ref={ref => {
            if (ref) {
              swipeableRefs.current[transaction.id] = ref;
            }
          }}
          renderRightActions={() => renderRightActions(transaction)}
          onSwipeableOpen={() => handleSwipeOpen(transaction.id)}
        >
          <TouchableOpacity
            style={[
              styles.transactionItemMinimalist,
              {
                backgroundColor: theme.surface,
                borderBottomColor: theme.border,
              }
            ]}
            onPress={() => handleEdit(transaction)}
          >
            <View style={styles.transactionLeftMinimalist}>
              <Text style={[styles.categoryIconMinimalist, {
                color: transaction.type === 'income' ? theme.income : theme.expense
              }]}>
                {categoryInfo?.icon || transaction.categoryIcon || '🧾'}
              </Text>
              <View style={styles.transactionInfoMinimalist}>
                <Text style={[styles.transactionTypeMinimalist, { color: theme.text }]} numberOfLines={1}>
                  {categoryInfo?.name || transaction.category}
                </Text>
                {transaction.note && (
                  <Text style={[styles.transactionNoteMinimalist, { color: theme.textTertiary }]} numberOfLines={1}>
                    {transaction.note}
                  </Text>
                )}
              </View>
            </View>
            <View style={styles.transactionRightMinimalist}>
              <Text style={[
                styles.transactionAmountMinimalist,
                { color: transaction.type === 'income' ? theme.income : theme.expense }
              ]}>
                {transaction.type === 'income' ? '+' : '-'}{currency}{Math.abs(transaction.amount).toFixed(2)}
              </Text>
              {!!memberName && !hideMemberSection && (
                <Text style={[styles.memberTagMinimalist, { color: theme.textSecondary }]} numberOfLines={1}>
                  {memberName}
                </Text>
              )}
            </View>
          </TouchableOpacity>
        </Swipeable>
      );
    }

    return (
      <Swipeable
        key={uniqueKey}
        ref={ref => {
          if (ref) {
            swipeableRefs.current[transaction.id] = ref;
          }
        }}
        renderRightActions={() => renderRightActions(transaction)}
        onSwipeableOpen={() => handleSwipeOpen(transaction.id)}
      >
        <TouchableOpacity
          style={[
            styles.transactionItem,
            {
              backgroundColor: theme.surface,
              borderColor: transaction.type === 'income'
                ? `${theme.income}90`  // 50% 透明度 (80 in hex = 128/255 ≈ 50%)
                : `${theme.expense}90`, // 50% 透明度
              borderWidth: 1
            }
          ]}
          onPress={() => handleEdit(transaction)}
        >
          <View style={styles.transactionLeft}>
            <View
              style={[
                styles.categoryIcon,
                {
                  backgroundColor: transaction.type === 'income'
                    ? theme.incomeBackground
                    : theme.expenseBackground
                }
              ]}
            >
              <Text style={styles.iconText}>
                {categoryInfo?.icon || transaction.categoryIcon || '🧾'}
              </Text>
            </View>
            <View style={styles.transactionInfo}>
              <View style={styles.transactionTitleRow}>
                <Text style={[styles.transactionType, { color: theme.text }]}>
                  {categoryInfo?.name || transaction.category}
                </Text>
                <View style={styles.transactionTags}>
                  {!!memberName && !hideMemberSection && (
                    <Text style={[styles.memberTag, {
                      color: theme.textSecondary,
                      backgroundColor: theme.background
                    }]}>{memberName}</Text>
                  )}
                  {/* {!!transaction.refunded && (
                    <View style={[styles.refundedBadge, { backgroundColor: theme.primary }]}>
                      <Text style={[styles.refundedText, { color: theme.surface }]}>
                        {i18n.t('common.refunded')}
                      </Text>
                    </View>
                  )} */}
                  {!!(transaction.reimbursement_status === 'pending') && (
                    <View style={[styles.reimbursementBadge, { backgroundColor: '#607D8B' }]}>
                      <Text style={[styles.reimbursementText, { color: 'white' }]}>
                        {i18n.t('reimbursement.pendingReimbursement')}
                      </Text>
                    </View>
                  )}
                  {!!(transaction.reimbursement_status === 'completed') && (
                    <View style={[styles.reimbursementBadge, { backgroundColor: '#4CAF50' }]}>
                      <Text style={[styles.reimbursementText, { color: 'white' }]}>
                        {i18n.t('reimbursement.reimbursed')}
                      </Text>
                    </View>
                  )}
                  {transaction.exclude_from_budget && (
                    <View style={[styles.excludeFromBudgetBadge, { backgroundColor: theme.textTertiary }]}>
                      <Text style={[styles.excludeFromBudgetText, { color: theme.textTertiary }]}>•</Text>
                    </View>
                  )}
                  {!!transaction.shopping_platform && !hideShoppingPlatformSection && (
                    <Text style={[styles.shoppingPlatformTag, {
                      color: theme.textSecondary,
                      backgroundColor: theme.background
                    }]}>{transaction.shopping_platform}</Text>
                  )}
                </View>
              </View>
              {transaction.note && (
                <Text style={[styles.transactionNote, { color: theme.textTertiary }]}>{transaction.note}</Text>
              )}
              {transaction.type === 'expense' && renderTags(transaction)}
            </View>
          </View>
          <View style={styles.transactionAmountContainer}>
            <Text style={[
              styles.transactionAmount,
              { color: transaction.type === 'income' ? theme.income : theme.expense }
            ]}>
              {transaction.type === 'income' ? '+' : '-'}{currency}{Math.abs(transaction.amount).toFixed(2)}
            </Text>
            {!!transaction.refund_amount && (transaction.refund_amount > 0) && (
              <Text style={[styles.refundAmountText, { color: '#FF9800' }]}>
                +{currency}{transaction.refund_amount.toFixed(2)}
              </Text>
            )}
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };

  const renderMemberSelectorModal = () => (
    <Modal
      visible={showMemberSelector}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowMemberSelector(false)}
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowMemberSelector(false)}
      >
        <View style={[styles.memberSelectorModal, {
          top: 160,
          right: 20,
        }]}>
          <TouchableOpacity
            style={[
              styles.memberSelectorItem,
              (selectedMembers.length === 0) && styles.selectedMemberItem
            ]}
            onPress={() => {
              const newSelection: number[] = [];
              setSelectedMembers(newSelection);
              saveSelectedMembers(newSelection);
              setShowMemberSelector(false);
            }}
          >
            <Text style={[
              styles.memberSelectorItemText,
              (selectedMembers.length === 0) && styles.selectedMemberItemText
            ]}>{i18n.t('common.allMembers')}</Text>
          </TouchableOpacity>
          {members.map(member => (
            <TouchableOpacity
              key={member.id}
              style={[
                styles.memberSelectorItem,
                (selectedMembers.length === 1 && selectedMembers[0] === member.id) && styles.selectedMemberItem
              ]}
              onPress={() => {
                // 单选逻辑
                let newSelection: number[];
                if (selectedMembers.length === 1 && selectedMembers[0] === member.id) {
                  newSelection = [];
                } else {
                  newSelection = [member.id];
                }
                setSelectedMembers(newSelection);
                saveSelectedMembers(newSelection);
                setShowMemberSelector(false);
              }}
            >
              <View style={styles.memberSelectorItemContent}>
                <Text style={[
                  styles.memberSelectorItemText,
                  (selectedMembers.length === 1 && selectedMembers[0] === member.id) && styles.selectedMemberItemText
                ]}>{member.name}</Text>
                {!!member.budget && (
                  <Text style={styles.memberBudgetText}>
                    {i18n.t('common.budget')}: {currency}{member.budget.toFixed(2)}
                  </Text>
                )}
              </View>
              {(selectedMembers.length === 1 && selectedMembers[0] === member.id) && (
                <Ionicons name="checkmark" size={20} color="#dc4446" />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </TouchableOpacity>
    </Modal>
  );

  // 退款类型选择模态框
  const renderRefundTypeModal = () => (
    <Modal
      visible={showRefundTypeModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowRefundTypeModal(false)}
      presentationStyle="overFullScreen"
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowRefundTypeModal(false)}
      >
        <View
          style={styles.refundModal}
          onStartShouldSetResponder={() => true}
          onTouchEnd={(e) => e.stopPropagation()}
        >
          <Text style={styles.refundModalTitle}>{i18n.t('common.selectRefundType')}</Text>
{/* 
          {!!selectedTransaction && (
            <View style={styles.refundInfo}>
              <Text style={styles.refundInfoText}>
                {i18n.t('common.originalAmount')}: {currency}{Math.abs(selectedTransaction.amount).toFixed(2)}
              </Text>
              {!!selectedTransaction.refund_amount && (selectedTransaction.refund_amount > 0) && (
                <Text style={styles.refundInfoText}>
                  {i18n.t('common.currentRefundAmount')}: {currency}{selectedTransaction.refund_amount.toFixed(2)}
                </Text>
              )}
            </View>
          )} */}

          <View style={styles.refundTypeButtons}>
            <TouchableOpacity
              style={[styles.refundTypeButton, styles.fullRefundButton]}
              onPress={handleFullRefund}
            >
              <Text style={styles.fullRefundButtonText}>{i18n.t('common.fullRefund')}</Text>
              {!!selectedTransaction && (
                <Text style={styles.refundAmountPreview}>
                  {currency}{Math.abs(selectedTransaction.amount).toFixed(2)}
                </Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.refundTypeButton, styles.partialRefundButton]}
              onPress={() => handleOpenPartialRefund()}
              activeOpacity={0.7}
            >
              <Text style={styles.partialRefundButtonText}>{i18n.t('common.partialRefund')}</Text>
              <Text style={styles.refundAmountPreview}>{i18n.t('common.customAmount')}</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.refundModalButtons}>
            <TouchableOpacity
              style={[styles.refundModalButton, styles.refundCancelButton]}
              onPress={() => setShowRefundTypeModal(false)}
            >
              <Text style={styles.refundCancelButtonText}>{i18n.t('common.cancel')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  // 部分退款模态框
  const renderRefundModal = () => (
    <Modal
      visible={showRefundModal}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowRefundModal(false)}
      presentationStyle="overFullScreen"
    >
      <TouchableOpacity
        style={styles.modalOverlay}
        activeOpacity={1}
        onPress={() => setShowRefundModal(false)}
      >
        <View
          style={styles.refundModal}
          onStartShouldSetResponder={() => true}
          onTouchEnd={(e) => e.stopPropagation()}
        >
          <Text style={styles.refundModalTitle}>{i18n.t('common.partialRefund')}</Text>

          {!!selectedTransaction && (
            <View style={styles.refundInfo}>
              <Text style={styles.refundInfoText}>
                {i18n.t('common.originalAmount')}: {currency}{Math.abs(selectedTransaction.amount).toFixed(2)}
              </Text>
              {!!selectedTransaction.refund_amount && (selectedTransaction.refund_amount > 0) && (
                <Text style={styles.refundInfoText}>
                  {i18n.t('common.currentRefundAmount')}: {currency}{selectedTransaction.refund_amount.toFixed(2)}
                </Text>
              )}
              <Text style={[styles.refundInfoText, { fontStyle: 'italic', color: '#666' }]}>
                {i18n.t('common.refundAmountNote')}
              </Text>
            </View>
          )}

          <TextInput
            style={styles.refundAmountInput}
            placeholder={i18n.t('common.enterRefundAmount')}
            value={refundAmount}
            onChangeText={setRefundAmount}
            keyboardType="numeric"
            autoFocus
          />

          <View style={styles.refundModalButtons}>
            <TouchableOpacity
              style={[styles.refundModalButton, styles.refundCancelButton]}
              onPress={() => setShowRefundModal(false)}
            >
              <Text style={styles.refundCancelButtonText}>{i18n.t('common.cancel')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.refundModalButton, styles.refundConfirmButton]}
              onPress={handlePartialRefund}
            >
              <Text style={styles.refundConfirmButtonText}>{i18n.t('common.confirm')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  // 计算当月剩余天数
  const getRemainingDaysInMonth = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();
    const lastDayOfMonth = new Date(year, month + 1, 0).getDate();
    const currentDay = now.getDate();
    return lastDayOfMonth - currentDay + 1; // 包括今天
  };

  const renderBudgetSection = () => {
    const stats = calculateSelectedMembersStats();

    const displayText = selectedMembers.length === 0
      ? i18n.t('common.allMembers')
      : members.find(m => m.id === selectedMembers[0])?.name || '';

    // 当隐藏成员模块时，始终使用总预算；否则使用总预算或成员预算
    const budgetValue = hideMemberSection
      ? (totalBudget || 0)
      : (selectedMembers.length === 0 && totalBudget !== null
        ? totalBudget
        : stats.budget || 0);

    // 使用一致的budgetValue计算进度和日均
    const progress = budgetValue ? (stats.expenses / budgetValue) * 100 : 0;
    const remainingDays = getRemainingDaysInMonth();
    const remainingBudget = Math.max(0, budgetValue - stats.expenses);
    const dailyAverage = (remainingDays > 0) ? (remainingBudget / remainingDays) : remainingBudget;

    // 检查是否需要显示预算设置提示
    const showBudgetPrompt = hideMemberSection
      ? (totalBudget === null || totalBudget === 0)
      : ((selectedMembers.length === 0 && (totalBudget === null || totalBudget === 0)) ||
         (selectedMembers.length > 0 && !stats.budget));

    return (
      <View style={styles.budgetSection}>
        <View style={styles.budgetHeader}>
          <Text style={styles.budgetTitle}>{i18n.t('common.monthlyBudget')}</Text>
          <View style={styles.budgetHeaderRight}>
            {/* 显示日均预算 */}
            {budgetValue > 0 && remainingDays > 0 && !showBudgetPrompt && (
              <Text style={styles.dailyAverageHeaderText}>
                {currency}{dailyAverage.toFixed(2)}/{i18n.t('common.day')}
              </Text>
            )}
            {!hideMemberSection && (
              <TouchableOpacity
                style={styles.memberSelector}
                onPress={() => setShowMemberSelector(true)}
              >
                <Text style={styles.memberSelectorText}>{displayText}</Text>
                <Ionicons name="chevron-down" size={16} color="#333" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {showBudgetPrompt ? (
          // 显示预算设置提示
          <TouchableOpacity
            style={styles.budgetPromptCard}
            onPress={() => router.push('/screens/budget')}
          >
            <View style={styles.budgetPromptContent}>
              <Ionicons name="alert-circle-outline" size={24} color="#dc4446" />
              <Text style={styles.budgetPromptText}>
                {i18n.t('common.noBudgetPrompt')}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>
        ) : (
          // 显示正常的预算卡片
          <View style={styles.budgetCard}>
            <Text style={styles.totalBudget}>{currency}{budgetValue.toFixed(2)}</Text>
            <View style={styles.budgetProgressBar}>
              <View style={[styles.budgetProgress, { width: `${Math.min(progress, 100)}%` }]} />
            </View>
            <View style={styles.budgetDetails}>
              <Text style={styles.budgetDetailText}>
                {i18n.t('common.used')}: {currency}{stats.expenses.toFixed(2)}
              </Text>
              <Text style={[
                styles.budgetDetailText,
                budgetValue && stats.expenses > budgetValue ? styles.overBudget : null
              ]}>
                {i18n.t('common.remaining')}: {currency}{(budgetValue - stats.expenses).toFixed(2)}
              </Text>
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderMonthlyStatsCard = () => {
    return <View style={[styles.monthlyStatsCard, { backgroundColor: theme.surface }]}>
      <View style={styles.monthlyStatsContent}>
        <View style={styles.monthlyStatsItem}>
          <Text style={[styles.monthlyStatsLabel, { color: theme.textSecondary }]}>{i18n.t('common.monthlyIncome')}</Text>
          <Text style={[styles.monthlyStatsAmount, { color: theme.income }]}>
            {currency}{actualMonthlyTotals.income.toFixed(2)}
          </Text>
        </View>
        <View style={[styles.monthlyStatsDivider, { backgroundColor: theme.border }]} />
        <View style={styles.monthlyStatsItem}>
          <Text style={[styles.monthlyStatsLabel, { color: theme.textSecondary }]}>{i18n.t('common.monthlyExpense')}</Text>
          <Text style={[styles.monthlyStatsAmount, { color: theme.expense }]}>
            {currency}{actualMonthlyTotals.expense.toFixed(2)}
          </Text>
        </View>
      </View>
    </View>
  }

  // 渲染加载更多
  const renderFooter = () => {
    // 检查是否有交易数据
    const hasTransactions = Object.keys(transactions).length > 0;

    if (hasMore) {
      return (
        <TouchableOpacity
          style={styles.loadMoreButton}
          onPress={() => loadTransactions(page + 1)}
          disabled={isLoading}
        >
          <Text style={[styles.loadMoreText, { color: theme.textSecondary }]}>
            {isLoading ? i18n.t('common.loading') : i18n.t('common.loadMore')}
          </Text>
        </TouchableOpacity>
      );
    } else if (hasTransactions) {
      // 只有在有数据的情况下才显示"没有更多了"
      return (
        <View style={styles.noMoreDataContainer}>
          <Text style={[styles.noMoreDataText, { color: theme.textTertiary }]}>
            {i18n.t('common.noMoreData')}
          </Text>
        </View>
      );
    }

    return null;
  };

  const renderSearchBar = () => (
    <View style={[styles.searchContainer, { borderBottomColor: theme.border }]}>
      <View style={[styles.searchInputContainer, { backgroundColor: theme.background }]}>
        <Ionicons name="search" size={20} color={theme.textSecondary} />
        <TextInput
          style={[styles.searchInput, { color: theme.text }]}
          placeholder={i18n.t('common.search')}
          placeholderTextColor={theme.textTertiary}
          value={searchText}
          onChangeText={setSearchText}
          returnKeyType="search"
          clearButtonMode="while-editing"
        />
        {searchText ? (
          <TouchableOpacity onPress={() => setSearchText('')}>
            <Ionicons name="close-circle" size={20} color={theme.textSecondary} />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );

  // 添加获取分类信息的辅助函数
  const getCategoryInfo = (categoryName: string, type: 'income' | 'expense') => {
    // 从缓存中获取分类信息
    const categoryList = type === 'income' ? incomeCategories : expenseCategories;
    return categoryList.find(c => c.name === categoryName);
  };

  // 添加处理左滑打开的函数
  const handleSwipeOpen = (id: number) => {
    // 关闭其他打开的左滑菜单
    Object.entries(swipeableRefs.current).forEach(([key, ref]) => {
      if (Number(key) !== id) {
        ref?.close();
      }
    });
  };


  const handleOpenPartialRefund = () => {
      console.log('Partial refund button pressed'); // Debug log
      setShowRefundTypeModal(false);
      setTimeout(() => {
        setShowRefundModal(true);
      }, 500);
  };

  const loadTotalBudget = async () => {
    try {
      const budget = await getTotalBudget();
      setTotalBudget(budget);
    } catch (error) {
      console.error('Failed to load total budget:', error);
    }
  };
  // 添加处理删除确认的函数
  const handleDeleteTransaction = (transaction: Transaction) => {
    Alert.alert(
      i18n.t('common.confirmDeleteTransaction'),
      i18n.t('common.confirmDeleteTransactionMessage'),
      [
        { text: i18n.t('common.cancel'), style: 'cancel' },
        {
          text: i18n.t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteTransaction(transaction.id);
              loadTransactions(page, true);
            } catch (error) {
              console.error('Failed to delete transaction:', error);
            }
          },
        },
      ]
    );
  };

  // 添加处理编辑的函数
  const handleEdit = (transaction: Transaction) => {
    router.push({
      pathname: '/screens/add',
      params: {
        mode: 'edit',
        id: transaction.id,
        type: transaction.type,
        amount: Math.abs(transaction.amount).toString(),
        category: transaction.category,
        categoryIcon: transaction.categoryIcon,
        note: transaction.note || '',
        date: transaction.date,
        initialTab: transaction.type,
        member_id: transaction.member_id,
        refunded: transaction.refunded ? 'true' : 'false',
        refund_amount: transaction.refund_amount?.toString() || '0',
        tags: transaction.tags?.join(','), // 将 tags 转换为字符串
        exclude_from_budget: transaction.exclude_from_budget ? 'true' : 'false',
        shopping_platform: transaction.shopping_platform,
        image_path: transaction.image_path || ''
      }
    });
  };

  // 处理报销状态切换
  const handleToggleReimbursement = async (transaction: Transaction) => {
    try {
      const newStatus = transaction.reimbursement_status === 'pending' ? 'none' : 'pending';
      await updateTransaction(transaction.id, {
        reimbursement_status: newStatus
      });
      loadTransactions(page, true);
      triggerRefresh();
    } catch (error) {
      console.error('Failed to toggle reimbursement status:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('reimbursement.updateReimbursementStatusFailed'));
    }
  };

  // 处理全额退款
  const handleFullRefund = async () => {
    if (!selectedTransaction) return;

    try {
      const originalAmount = Math.abs(selectedTransaction.amount);

      await updateTransaction(selectedTransaction.id, {
        refund_amount: originalAmount,
        refunded: true
      });

      setShowRefundTypeModal(false);
      setSelectedTransaction(null);
      loadTransactions(page, true);
      triggerRefresh(); // 触发全局刷新
    } catch (error) {
      console.error('Failed to process full refund:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('common.refundFailed'));
    }
  };

  // 处理部分退款
  const handlePartialRefund = async () => {
    if (!selectedTransaction || !refundAmount) {
      Alert.alert(i18n.t('common.error'), i18n.t('common.pleaseEnterAmount'));
      return;
    }

    const refundAmountNum = parseFloat(refundAmount);
    const originalAmount = Math.abs(selectedTransaction.amount);

    if (refundAmountNum <= 0 || refundAmountNum > originalAmount) {
      Alert.alert(
        i18n.t('common.error'),
        i18n.t('common.invalidRefundAmount', { max: originalAmount.toFixed(2) })
      );
      return;
    }

    try {
      // 直接使用新输入的退款金额，覆盖之前的退款金额
      const newRefundAmount = refundAmountNum;
      const isFullyRefunded = newRefundAmount >= originalAmount;

      await updateTransaction(selectedTransaction.id, {
        refund_amount: newRefundAmount,
        refunded: isFullyRefunded
      });

      setShowRefundModal(false);
      setSelectedTransaction(null);
      setRefundAmount('');
      loadTransactions(page, true);
      triggerRefresh(); // 触发全局刷新
    } catch (error) {
      console.error('Failed to process refund:', error);
      Alert.alert(i18n.t('common.error'), i18n.t('common.refundFailed'));
    }
  };

  useEffect(() => {
    loadTotalBudget();
    // 确保在组件挂载时立即加载月度总计
    loadActualMonthlyTotals();
  }, [refreshTrigger]);

  // 加载个人留言
  const loadPersonalNote = async () => {
    try {
      const note = await getSetting('personalNote');
      if (note) {
        setPersonalNote(note);

        // 检查是否是今天第一次打开
        const today = new Date().toDateString();
        const lastShownDate = await AsyncStorage.getItem('personalNoteLastShown');

        if (lastShownDate !== today) {
          setPersonalNoteVisible(true);
          await AsyncStorage.setItem('personalNoteLastShown', today);
        }
      }
    } catch (error) {
      console.error('Failed to load personal note:', error);
    }
  };

  useEffect(() => {
    loadPersonalNote();
  }, [refreshTrigger]); // 当刷新触发时重新加载个人留言

  // 渲染个人留言
  const renderPersonalNote = () => {
    if (!personalNote.trim() || !personalNoteVisible) return null;

    return (
      <View style={[styles.personalNoteCard, {
        backgroundColor: '#FFF8E1', // 更显眼的背景色 - 浅黄色
        borderColor: '#FFB74D',
        borderWidth: 1
      }]}>
        <View style={styles.personalNoteHeader}>
          <View style={styles.personalNoteLeft}>
            <Ionicons name="chatbubble" size={16} color="#FF8F00" />
            <Text style={[styles.personalNoteTitle, { color: '#E65100', fontWeight: '600' }]}>
              {i18n.t('settings.personalNote')}
            </Text>
          </View>
          <TouchableOpacity
            onPress={async () => {
              setPersonalNoteVisible(false);
              // 记录今天已经关闭，当天不再显示
              const today = new Date().toDateString();
              await AsyncStorage.setItem('personalNoteLastShown', today);
            }}
            style={styles.personalNoteCloseButton}
          >
            <Ionicons name="close" size={18} color="#FF8F00" />
          </TouchableOpacity>
        </View>
        <Text style={[styles.personalNoteText, { color: '#BF360C', fontWeight: '500' }]}>
          {personalNote}
        </Text>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]} onTouchStart={closeAllSwipeables}>
      {/* {renderButtonGroup()} */}
      {!hideBudgetModule && renderBudgetSection()}
      {!hideMemberSection && renderMemberSelectorModal()}
      {renderRefundTypeModal()}
      {renderRefundModal()}
      {renderPersonalNote()}
      {renderMonthlyStatsCard()}

      <View style={[styles.transactionSection, { backgroundColor: theme.surface }]}>
        <View style={styles.transactionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.text }]}>{i18n.t('common.transactionRecord')}</Text>
          <View style={[styles.filters, { borderColor: theme.border }]}>
            <TouchableOpacity
              style={[
                styles.filter,
                activeFilter === 'all' && { backgroundColor: theme.primaryLight }
              ]}
              onPress={() => setActiveFilter('all')}
            >
              <Text style={[
                { color: theme.textSecondary },
                activeFilter === 'all' && { color: theme.primary, fontWeight: '500' }
              ]}>{i18n.t('common.all')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.filter,
                activeFilter === 'income' && { backgroundColor: theme.primaryLight }
              ]}
              onPress={() => setActiveFilter('income')}
            >
              <Text style={[
                { color: theme.textSecondary },
                activeFilter === 'income' && { color: theme.primary, fontWeight: '500' }
              ]}>{i18n.t('common.income')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.filter,
                activeFilter === 'expense' && { backgroundColor: theme.primaryLight }
              ]}
              onPress={() => setActiveFilter('expense')}
            >
              <Text style={[
                { color: theme.textSecondary },
                activeFilter === 'expense' && { color: theme.primary, fontWeight: '500' }
              ]}>{i18n.t('common.expense')}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.searchButton}
              onPress={() => setShowSearch(!showSearch)}
            >
              <Ionicons
                name={showSearch ? "close" : "search"}
                size={20}
                color={theme.textSecondary}
              />
            </TouchableOpacity>

          </View>
        </View>

        {showSearch && renderSearchBar()}

        <ScrollView
          style={styles.transactionList}
          contentContainerStyle={styles.transactionListContent}
          onScrollBeginDrag={closeAllSwipeables}
          onScroll={({ nativeEvent }) => {
            const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
            const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

            if (isCloseToBottom && !isLoading && hasMore) {
              loadTransactions(page + 1);
            }
          }}
          scrollEventThrottle={400}
          showsVerticalScrollIndicator={false}
        >
          {Object.keys(transactions).length === 0 && (
            <EmptyState
              icon="receipt-outline"
              title={i18n.t('common.noTransactions')}
              description={i18n.t('common.clickAddButtonToRecord')}
            />
          )}
          {Object.entries(transactions)
            .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
            .map(([date, items]) => (
              <View key={`date-${date}`} style={styles.dateGroup}>
                <View style={styles.dateHeader}>
                  <Text style={[styles.dateText, { color: theme.textSecondary }]}>{formatDate(date)}</Text>
                  <View style={styles.dailyTotal}>
                    {calculateDailyTotal(items).income > 0 && (
                      <Text style={[styles.dailyTotalText, { color: theme.income }]}>
                        {i18n.t('common.income')}: {currency}{calculateDailyTotal(items).income.toFixed(2)}
                      </Text>
                    )}
                    {calculateDailyTotal(items).expense > 0 && (
                      <Text style={[styles.dailyTotalText, { color: theme.expense }]}>
                        {i18n.t('common.expense')}: {currency}{calculateDailyTotal(items).expense.toFixed(2)}
                      </Text>
                    )}
                  </View>
                </View>
                {items.map((transaction, index) => (
                  renderTransactionItem(transaction, index)
                ))}
              </View>
            ))}

          {renderFooter()}
        </ScrollView>
      </View>
    </View >
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
    marginTop: 40,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  buttons: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 24,
  },
  incomeButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
  },
  expenseButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  incomeButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  expenseButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  transactionSection: {
    borderRadius: 16,
    padding: 16,
    flex: 1,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  searchButton: {
    padding: 4,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 10,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    padding: 0,
  },
  transactionList: {
    flex: 1,
  },
  transactionListContent: {
    paddingBottom: 100, // 确保可以滚动到底部
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1, // 允许左侧内容占用剩余空间，但不会挤压右侧金额
    minWidth: 0, // 允许内容收缩
  },
  transactionIcon: {
    width: 44,
    height: 44,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    fontSize: 20,
  },
  transactionInfo: {
    gap: 4,
    flex: 1, // 允许信息区域占用剩余空间
    minWidth: 0, // 允许内容收缩
  },
  transactionInfoCenter: {
    justifyContent: 'center',
  },
  transactionType: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  transactionDate: {
    color: '#999',
    fontSize: 13,
  },
  transactionAmount: {
    fontWeight: '600',
    fontSize: 17,
    color: '#333',
    flexShrink: 0, // 防止金额被压缩
    minWidth: 80, // 确保金额有最小宽度
    textAlign: 'right', // 右对齐显示
  },
  navbar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 12,
    backgroundColor: 'white',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 5,
  },
  navItem: {
    alignItems: 'center',
    gap: 4,
  },
  navText: {
    color: '#999',
  },
  activeNavText: {
    color: '#dc4446',
  },
  floatingButton: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#dc4446',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#dc4446',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  floatingButtonText: {
    color: 'white',
    fontSize: 24,
  },
  deleteAction: {
    backgroundColor: '#dc4446',
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
    height: '100%',
    borderRadius: 12,
  },
  dateGroup: {
    marginBottom: 20,
  },
  dateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateText: {
    fontSize: 14,
    color: '#666',
  },
  dailyTotal: {
    flexDirection: 'row',
    gap: 12,
  },
  dailyTotalText: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    width: 160,
    height: '100%',
  },
  actionButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  editButton: {
    backgroundColor: '#4CAF50',
  },
  deleteButton: {
    backgroundColor: '#dc4446',
  },
  refundButton: {
    backgroundColor: '#FF9A2E',
  },
  reimbursementButton: {
    backgroundColor: '#607D8B',
  },
  filters: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 1,
    marginBottom: 16,
    padding: 2,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 16,
  },
  filter: {
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 20,
  },
  activeFilter: {
    backgroundColor: '#FFF1F1',
  },
  filterText: {
    color: '#666',
    fontSize: 14,
  },
  activeFilterText: {
    color: '#dc4446',
    fontWeight: '500',
  },
  transactionNote: {
    color: '#999',
    fontSize: 12,
    flexShrink: 1, // 允许备注文本收缩
    flexWrap: 'wrap', // 允许文本换行
  },
  refundedBadge: {
    backgroundColor: '#dc4446',
    borderRadius: 12,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  refundedText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  reimbursementBadge: {
    borderRadius: 12,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  reimbursementText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  transactionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  monthlyStatsCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  monthlyStatsTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 12,
  },
  monthlyStatsContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  monthlyStatsItem: {
    flex: 1,
    alignItems: 'center',
  },
  monthlyStatsDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#eee',
    marginHorizontal: 16,
  },
  monthlyStatsLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  monthlyStatsAmount: {
    fontSize: 20,
    fontWeight: '600',
  },
  transactionTags: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  memberTag: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f5f5f5',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  shoppingPlatformTag: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f5f5f5',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  memberFilter: {
    marginBottom: 16,
  },
  memberFilterItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    marginRight: 8,
  },
  selectedMemberFilter: {
    backgroundColor: '#fff1f1',
  },
  memberFilterText: {
    fontSize: 14,
    color: '#666',
  },
  selectedMemberFilterText: {
    color: '#dc4446',
    fontWeight: '500',
  },
  budgetInfo: {
    marginTop: 4,
  },
  budgetText: {
    fontSize: 12,
    color: '#666',
  },
  remainingText: {
    fontSize: 12,
    color: '#4CAF50',
  },
  overBudget: {
    color: '#dc4446',
  },
  budgetSection: {
    marginBottom: 20,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  budgetTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  budgetHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dailyAverageHeaderText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4CAF50',
    backgroundColor: '#f0f8f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  memberSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: '#fff1f1',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  memberSelectorText: {
    fontSize: 14,
    color: '#dc4446',
  },
  budgetCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
  },
  totalBudget: {
    fontSize: 24,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  budgetProgressBar: {
    height: 8,
    backgroundColor: '#f5f5f5',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  budgetProgress: {
    height: '100%',
    backgroundColor: '#dc4446',
    borderRadius: 4,
  },
  budgetDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  budgetDetailText: {
    fontSize: 14,
    color: '#666',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  memberSelectorModal: {
    position: 'absolute',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 8,
    minWidth: 160,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  memberSelectorItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  selectedMemberItem: {
    backgroundColor: '#fff1f1',
  },
  memberSelectorItemText: {
    fontSize: 16,
    color: '#333',
  },
  selectedMemberItemText: {
    color: '#dc4446',
    fontWeight: '500',
  },
  memberBudgetText: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  memberSelectorItemContent: {
    flex: 1,
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 4,
    marginTop: 4,
  },
  tag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    borderWidth: 1,
    backgroundColor: 'white',
  },
  tagText: {
    fontSize: 10,
  },
  moreTagsText: {
    fontSize: 10,
    color: '#666',
  },
  moreTagsContainer: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
  },
  loadMoreButton: {
    padding: 16,
    alignItems: 'center',
  },
  loadMoreText: {
    fontSize: 14,
  },
  noMoreDataContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noMoreDataText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  categoryIcon: {
    width: 44,
    height: 44,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryIconText: {
    fontSize: 20,
  },
  transactionCategory: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  amountText: {
    fontWeight: '600',
    fontSize: 17,
  },
  budgetPromptCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  budgetPromptContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  budgetPromptText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  excludeFromBudgetBadge: {
    marginLeft: 4,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#999',
    alignSelf: 'center',
  },
  excludeFromBudgetText: {
    fontSize: 10,
    color: '#999',
  },
  refundModal: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    margin: 20,
    marginTop: '50%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  refundModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
    color: '#333',
  },
  refundInfo: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  refundInfoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  refundAmountInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
    backgroundColor: '#fff',
  },
  refundModalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  refundModalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  refundCancelButton: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  refundConfirmButton: {
    backgroundColor: '#dc4446',
  },
  refundCancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  refundConfirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  personalNoteCard: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
    borderLeftWidth: 4,
    borderLeftColor: '#FF8F00',
  },
  personalNoteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  personalNoteLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  personalNoteTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
    flex: 1,
  },
  personalNoteCloseButton: {
    padding: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 143, 0, 0.1)',
  },
  personalNoteText: {
    fontSize: 14,
    lineHeight: 20,
    marginTop: 8,
  },
  transactionAmountContainer: {
    alignItems: 'flex-end',
  },
  refundAmountText: {
    fontSize: 12,
    marginTop: 2,
  },
  refundTypeButtons: {
    marginVertical: 20,
    gap: 12,
  },
  refundTypeButton: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: '#F8F9FA',
    alignItems: 'center',
  },
  fullRefundButton: {
    backgroundColor: '#E3F2FD',
    borderColor: '#2196F3',
  },
  partialRefundButton: {
    backgroundColor: '#FFF3E0',
    borderColor: '#FF9800',
  },
  fullRefundButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2196F3',
    marginBottom: 4,
  },
  partialRefundButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF9800',
    marginBottom: 4,
  },
  refundAmountPreview: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  // 极简模式样式
  transactionItemMinimalist: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
    minHeight: 44,
  },
  transactionLeftMinimalist: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 12,
  },
  categoryIconMinimalist: {
    fontSize: 16,
    marginRight: 8,
    width: 20,
    textAlign: 'center',
  },
  transactionInfoMinimalist: {
    flex: 1,
    justifyContent: 'center',
  },
  transactionTypeMinimalist: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 18,
  },
  transactionNoteMinimalist: {
    fontSize: 12,
    lineHeight: 16,
    marginTop: 1,
  },
  transactionRightMinimalist: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  transactionAmountMinimalist: {
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 18,
  },
  memberTagMinimalist: {
    fontSize: 11,
    lineHeight: 14,
    marginTop: 1,
  },
});

export default HomeList;
